import React, { useState, useRef, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useFormContext } from 'react-hook-form';
import {
  Button,
  Form,
  FormItem,
  Input,
  Select,
  Icon,
  Typography,
  Loading,
  CollapsibleCard,
  PhoneInputWithCountryFormField,
  DatePickerFormField,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { FormStatus } from '../types/profile.types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { PROFILE_CARD_IDS } from '../constants/profile-cards';
import { useCurrentUser, useUpdatePersonalInfo, useSendPhoneOtp } from '../hooks/useUser';
import { GenderEnum, UpdatePersonalInfoDto } from '../types/user.types';
import { createPersonalInfoSchema, PersonalInfoSchema } from '../schemas';
import ProfileCard from './ProfileCard';
import PhoneVerificationModal from './PhoneVerificationModal';

/**
 * Component form thông tin cá nhân
 */
// Component để kiểm tra lỗi validation và disable button
const VerifyPhoneButton: React.FC<{
  onClick: () => void;
  isLoading: boolean;
  t: any;
}> = ({ onClick, isLoading, t }) => {
  const { watch, formState } = useFormContext();
  const phoneNumberValue = watch('phoneNumber');

  // Kiểm tra xem số điện thoại có hợp lệ không (format quốc tế không có dấu +: 84963457232, 1234567890, etc.)
  const isValidPhone = phoneNumberValue && /^[1-9]\d{6,14}$/.test(phoneNumberValue);
  const hasError = formState.errors['phoneNumber'];



  return (
    <Button
      variant="outline"
      onClick={onClick}
      isLoading={isLoading}
      disabled={isLoading || !isValidPhone || !!hasError}
      className="flex-shrink-0"
    >
      {t('profile:phoneVerification.verifyButton', 'Xác thực')}
    </Button>
  );
};

const PersonalInfoForm: React.FC = () => {
  const { t } = useTranslation(['profile', 'validation']);
  const [formStatus, setFormStatus] = useState<FormStatus>(FormStatus.IDLE);
  const [showPhoneVerificationModal, setShowPhoneVerificationModal] = useState(false);
  const [currentPhoneNumber, setCurrentPhoneNumber] = useState<string>('');
  const { showNotification } = useSmartNotification();

  // Ref để truy cập form methods
  const formRef = useRef<FormRef<PersonalInfoSchema>>(null);

  // Tạo schema với hàm t để hỗ trợ đa ngôn ngữ - sử dụng useMemo để tránh tạo lại schema không cần thiết
  const personalInfoSchema = useMemo(() => createPersonalInfoSchema(t), [t]);

  // Sử dụng hook để lấy thông tin người dùng - chỉ khi card được mở
  const { data: user, isLoading: isLoadingUser, error: userError } = useCurrentUser();

  // Sử dụng hook để cập nhật thông tin cá nhân
  const updatePersonalInfoMutation = useUpdatePersonalInfo();

  // Sử dụng hook để gửi OTP xác thực số điện thoại
  const sendPhoneOtpMutation = useSendPhoneOtp();

  // Reset form khi user data thay đổi
  useEffect(() => {
    if (user && formRef.current) {
      formRef.current.reset({
        fullName: user.fullName || '',
        gender: user.gender || GenderEnum.MALE,
        dateOfBirth: user.dateOfBirth || '',
        address: user.address || '',
        email: user.email || '',
        phoneNumber: user.phoneNumber || '',
      });
    }
  }, [user]);

  // Xử lý khi submit form
  const onSubmit = (data: PersonalInfoSchema) => {
    // Sử dụng dữ liệu từ form
    const submittingData: PersonalInfoSchema = data;

    // Kiểm tra dữ liệu trước khi gửi
    if (!submittingData.fullName) {
      showNotification('error', t('validation:required', 'Họ và tên là bắt buộc'));
      return;
    }

    if (!submittingData.gender) {
      showNotification('error', t('validation:required', 'Giới tính là bắt buộc'));
      return;
    }

    if (!submittingData.address) {
      showNotification('error', t('validation:required', 'Địa chỉ là bắt buộc'));
      return;
    }

    if (!submittingData.email) {
      showNotification('error', t('validation:required', 'Email là bắt buộc'));
      return;
    }

    if (!submittingData.phoneNumber) {
      showNotification('error', t('validation:required', 'Số điện thoại là bắt buộc'));
      return;
    }

    // Cập nhật trạng thái form
    setFormStatus(FormStatus.SUBMITTING);

    // Chuyển đổi dữ liệu từ form sang DTO
    const updateData: UpdatePersonalInfoDto = {
      fullName: submittingData.fullName,
      gender: submittingData.gender,
      ...(submittingData.dateOfBirth && { dateOfBirth: submittingData.dateOfBirth }),
      address: submittingData.address,
      phoneNumber: submittingData.phoneNumber,
    };

    // Gọi API để cập nhật thông tin
    updatePersonalInfoMutation.mutate(updateData, {
      onSuccess: response => {
        console.log('Update success:', response);
        setFormStatus(FormStatus.IDLE);

        showNotification(
          'success',
          t('profile:messages.updateSuccess', 'Cập nhật thông tin cá nhân thành công')
        );
      },
      onError: error => {
        console.error('Error updating profile:', error);
        setFormStatus(FormStatus.IDLE);

        // Hiển thị thông báo lỗi
        let errorMessage = t(
          'profile:messages.updateError',
          'Có lỗi xảy ra khi cập nhật thông tin cá nhân'
        );

        // Kiểm tra xem error có phải là AxiosError không
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as { response?: { data?: { message?: string } } };
          if (axiosError.response?.data?.message) {
            errorMessage = axiosError.response.data.message;
          }
        }

        showNotification('error', errorMessage);
      },
    });
  };

  // Xử lý khi hủy thay đổi (reset về giá trị ban đầu)
  const handleCancel = (e?: React.MouseEvent) => {
    // Ngăn chặn sự kiện mặc định (nếu có) để tránh gửi form
    if (e) {
      e.preventDefault();
    }

    // Reset form về giá trị ban đầu từ user data
    if (formRef.current && user) {
      formRef.current.reset({
        fullName: user.fullName || '',
        gender: user.gender || GenderEnum.MALE,
        dateOfBirth: user.dateOfBirth || '',
        address: user.address || '',
        email: user.email || '',
        phoneNumber: user.phoneNumber || '',
      });
    }
  };

  // Xử lý xác thực số điện thoại
  const handleVerifyPhone = () => {
    // Lấy giá trị số điện thoại hiện tại từ form
    const currentFormData = formRef.current?.getValues();
    const currentPhoneNumber = currentFormData?.phoneNumber;

    if (!currentPhoneNumber) {
      showNotification('error', t('profile:phoneVerification.noPhoneNumber', 'Vui lòng nhập số điện thoại trước khi xác thực.'));
      return;
    }

    sendPhoneOtpMutation.mutate(currentPhoneNumber, {
      onSuccess: () => {
        showNotification(
          'success',
          t('profile:phoneVerification.otpSent', 'Mã OTP đã được gửi đến số điện thoại của bạn.')
        );
        // Lưu số điện thoại hiện tại để truyền vào modal
        setCurrentPhoneNumber(currentPhoneNumber);
        setShowPhoneVerificationModal(true);
      },
      onError: (error) => {
        console.error('Send OTP failed:', error);

        // Xử lý lỗi từ API
        let errorMessage = t('profile:phoneVerification.sendOtpError', 'Không thể gửi mã OTP. Vui lòng thử lại.');

        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as { response?: { data?: { message?: string; code?: number } } };
          if (axiosError.response?.data?.message) {
            errorMessage = axiosError.response.data.message;
          } else if (axiosError.response?.data?.code === 400) {
            errorMessage = t('profile:phoneVerification.phoneAlreadyUsed', 'Số điện thoại này đã được sử dụng bởi tài khoản khác');
          } else if (axiosError.response?.data?.code === 429) {
            errorMessage = t('profile:phoneVerification.rateLimited', 'Vui lòng chờ 60 giây trước khi gửi lại OTP');
          }
        }

        showNotification('error', errorMessage);
      },
    });
  };

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoadingUser) {
    return (
      <CollapsibleCard title={t('profile:personalInfo.title')} className="mb-6">
        <div className="flex justify-center items-center py-8">
          <Loading size="lg" />
          <span className="ml-2">{t('profile:loading.loadingProfile')}</span>
        </div>
      </CollapsibleCard>
    );
  }

  // Hiển thị lỗi nếu có
  if (userError) {
    return (
      <CollapsibleCard title={t('profile:personalInfo.title')} className="mb-6">
        <div className="text-red-500 py-4">{t('profile:error.loadingProfile')}</div>
      </CollapsibleCard>
    );
  }

  // Danh sách giới tính
  const genderOptions = [
    { value: GenderEnum.MALE, label: t('profile:personalInfo.genderOptions.male') },
    { value: GenderEnum.FEMALE, label: t('profile:personalInfo.genderOptions.female') },
    { value: GenderEnum.OTHER, label: t('profile:personalInfo.genderOptions.other') },
  ];

  const cardTitle = (
    <div className="flex items-center">
      <Icon name="user" className="mr-2 text-primary" />
      <Typography variant="subtitle1" weight="semibold" color="dark">
        {t('profile:personalInfo.title')}
      </Typography>
    </div>
  );

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoadingUser) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.PERSONAL_INFO} title={cardTitle}>
        <div className="flex justify-center items-center py-8">
          <Loading size="lg" />
          <span className="ml-2">{t('profile:loading.loadingProfile')}</span>
        </div>
      </ProfileCard>
    );
  }

  // Hiển thị lỗi nếu có
  if (userError) {
    return (
      <ProfileCard cardId={PROFILE_CARD_IDS.PERSONAL_INFO} title={cardTitle}>
        <div className="text-red-500 py-4">{t('profile:error.loadingProfile')}</div>
      </ProfileCard>
    );
  }

  return (
    <ProfileCard cardId={PROFILE_CARD_IDS.PERSONAL_INFO} title={cardTitle}>
      <Form
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ref={formRef as any}
        schema={personalInfoSchema}
        mode="onSubmit"
        onSubmit={data => {
          // Kiểm tra xem form có đang ở trạng thái submitting không
          if (formStatus === FormStatus.SUBMITTING) {
            return;
          }
          onSubmit(data as PersonalInfoSchema);
        }}
        defaultValues={{
          fullName: user?.fullName || '',
          gender: user?.gender || GenderEnum.MALE,
          dateOfBirth: user?.dateOfBirth || '',
          address: user?.address || '',
          email: user?.email || '',
          phoneNumber: user?.phoneNumber || '',
        }}
      >
        <div className="space-y-6">
          {/* Họ và tên */}
          <FormItem name="fullName" label={t('profile:personalInfo.fullName')} required>
            <Input placeholder={t('profile:personalInfo.fullName')} fullWidth />
          </FormItem>

          {/* Giới tính */}
          <FormItem name="gender" label={t('profile:personalInfo.gender')} required>
            <Select
              options={genderOptions}
              placeholder={t('profile:personalInfo.gender')}
              fullWidth
            />
          </FormItem>

          {/* Ngày sinh */}
          <FormItem name="dateOfBirth" label={t('profile:personalInfo.birthDate')}>
            <DatePickerFormField
              placeholder={t('profile:personalInfo.birthDate')}
              fullWidth
              stringFormat="yyyy-MM-dd"
            />
          </FormItem>

          {/* Địa chỉ */}
          <FormItem name="address" label={t('profile:personalInfo.address')} required>
            <Input placeholder={t('profile:personalInfo.address')} fullWidth />
          </FormItem>

          {/* Email */}
          <FormItem
            name="email"
            label={
              <div className="flex items-center">
                <span>{t('profile:personalInfo.email')}</span>
                {user && user.isVerifyEmail && (
                  <Icon name="check" className="ml-2 text-green-500" />
                )}
              </div>
            }
            required={!user?.isVerifyEmail}
          >
            <Input
              disabled={user && user.isVerifyEmail}
              placeholder={t('profile:personalInfo.email')}
              type="email"
              fullWidth
            />
          </FormItem>

          {/* Số điện thoại */}
          <FormItem
            name="phoneNumber"
            label={
              <div className="flex items-center">
                <span>{t('profile:personalInfo.phone')}</span>
                {user && user.isVerifyPhone && (
                  <Icon name="check" className="ml-2 text-green-500" />
                )}
              </div>
            }
            required={!user?.isVerifyPhone}
          >
            <div className="flex items-center space-x-3">
              <div className="flex-1">
                <PhoneInputWithCountryFormField
                  name="phoneNumber"
                  disabled={Boolean(user && user.isVerifyPhone)}
                  placeholder={t('profile:personalInfo.phone')}
                  fullWidth
                  defaultCountry="VN"
                />
              </div>
              {user && !user.isVerifyPhone && (
                <VerifyPhoneButton
                  onClick={handleVerifyPhone}
                  isLoading={sendPhoneOtpMutation.isPending}
                  t={t}
                />
              )}
            </div>
          </FormItem>

          {/* Buttons - Luôn hiển thị để test */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              type="button"
              onClick={e => handleCancel(e)}
              disabled={formStatus === FormStatus.SUBMITTING}
            >
              {t('profile:buttons.cancel')}
            </Button>
            <Button
              variant="primary"
              type="submit"
              isLoading={formStatus === FormStatus.SUBMITTING}
            >
              {t('profile:buttons.save')}
            </Button>
          </div>
        </div>
      </Form>

      {/* Modal xác thực số điện thoại */}
      <PhoneVerificationModal
        isOpen={showPhoneVerificationModal}
        onClose={() => {
          setShowPhoneVerificationModal(false);
          setCurrentPhoneNumber('');
        }}
        phoneNumber={currentPhoneNumber}
        onSuccess={() => {
          // Refresh user data sau khi xác thực thành công
          // Hook useCurrentUser sẽ tự động invalidate và refetch
          setCurrentPhoneNumber('');
        }}
      />
    </ProfileCard>
  );
};

export default PersonalInfoForm;
