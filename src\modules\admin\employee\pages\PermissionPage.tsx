/**
 * Trang quản lý quyền (permissions)
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import useNotification from '@/shared/hooks/common/useNotification';
import NotificationContainer from '@/shared/components/layout/chat-panel/NotificationContainer';
import { PermissionDto } from '../types/employee.types';
import { useAllPermissions } from '../hooks/usePermissionQuery';

/**
 * Trang quản lý quyền
 */
const PermissionPage: React.FC = () => {
  const { t } = useTranslation(['employee', 'common']);
  const { notifications, removeNotification } = useNotification();

  // State lưu trữ dữ liệu quyền
  const [permissions, setPermissions] = useState<PermissionDto[]>([]);

  // Queries
  const permissionsQuery = useAllPermissions();

  // Effect to update state when API data is available
  useEffect(() => {
    if (permissionsQuery.data) {
      setPermissions(permissionsQuery.data);
    }
  }, [permissionsQuery.data]);

  // Columns cho bảng
  const columns: TableColumn<PermissionDto>[] = [
    { key: 'id', title: 'ID', dataIndex: 'id', width: '5%' },
    {
      key: 'module',
      title: t('employee:permission.module'),
      dataIndex: 'module',
      width: '15%',
    },
    {
      key: 'action',
      title: t('employee:permission.action'),
      dataIndex: 'action',
      width: '20%',
    },
    {
      key: 'description',
      title: t('employee:permission.description'),
      dataIndex: 'description',
      width: '40%',
    },
    {
      key: 'createdAt',
      title: t('common:createdAt'),
      dataIndex: 'createdAt',
      width: '10%',
      render: (value: unknown) => {
        const timestamp = value as number;
        return new Date(timestamp * 1000).toLocaleDateString('vi-VN');
      },
    },
    // {
    //   key: 'actions',
    //   title: t('common:actions'),
    //   width: '10%',
    //   render: (_: unknown, record: PermissionDto) => {
    //     const actionItems = [];

    //     return (
    //       <div className="flex justify-center">
    //         <ActionMenu
    //           items={actionItems}
    //           menuTooltip={t('common:moreActions')}
    //           iconSize="sm"
    //           iconVariant="default"
    //           placement="bottom"
    //           menuWidth="120px"
    //           showAllInMenu={true}
    //           preferRight={true}
    //           preferTop={true}
    //         />
    //       </div>
    //     );
    //   },
    // },
  ];

  return (
    <div>
      <MenuIconBar
        onSearch={() => {}} // TODO: Implement search
        items={[]}
        columns={[]}
        showColumnFilter={false}
      />

      <Card className="overflow-hidden">
        <Table
          columns={columns}
          data={permissions}
          rowKey="id"
          loading={permissionsQuery.isLoading}
          sortable={true}
        />
      </Card>

      {/* Notification container */}
      <NotificationContainer notifications={notifications} onRemove={removeNotification} />
    </div>
  );
};

export default PermissionPage;
