import { z } from 'zod';
import { GenderEnum } from '../types/user.types';
import { TFunction } from 'i18next';

/**
 * Schema creator function for personal info form with translation support
 */
export const createPersonalInfoSchema = (t: TFunction) =>
  z.object({
    fullName: z.string().min(1, t('validation:fullName.required')),
    gender: z.nativeEnum(GenderEnum),
    dateOfBirth: z.union([z.string(), z.date()]).optional(),
    address: z.string().min(1, t('validation:address.required')),
    email: z.string().email(t('validation:email.invalid')).min(1, t('validation:email.required')),
    phoneNumber: z
      .string()
      .min(1, t('validation:phone.required'))
      .regex(/^[1-9]\d{6,14}$/, t('validation:phoneInternational')),
  });

/**
 * Type for personal info schema
 */
export type PersonalInfoSchema = z.infer<ReturnType<typeof createPersonalInfoSchema>>;
