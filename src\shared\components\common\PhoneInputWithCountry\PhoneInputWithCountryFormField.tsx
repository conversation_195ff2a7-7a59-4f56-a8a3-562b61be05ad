import { forwardRef } from 'react';
import { useController, useFormContext } from 'react-hook-form';
import PhoneInputWithCountry, { PhoneInputWithCountryProps } from './PhoneInputWithCountry';

interface PhoneInputWithCountryFormFieldProps extends Omit<PhoneInputWithCountryProps, 'error' | 'helperText'> {
  // Loại bỏ error và helperText để FormItem xử lý
  name?: string; // Thêm name prop để có thể sử dụng useController
}

/**
 * PhoneInputWithCountry component tích hợp với React Hook Form
 * Component này đảm bảo value được sync đúng cách với React Hook Form
 */
const PhoneInputWithCountryFormField = forwardRef<HTMLInputElement, PhoneInputWithCountryFormFieldProps>(
  (props, ref) => {

    // Thử sử dụng useController nếu có name và không có value từ props
    let formContext = null;
    let controllerField = null;

    try {
      formContext = useFormContext();
    } catch (error) {
      // Không có form context
    }

    if (props.name && formContext && (!props.value || props.value === '')) {
      try {
        const controller = useController({
          name: props.name,
          control: formContext.control,
        });
        controllerField = controller.field;
      } catch (error) {
        // Error using controller
      }
    }

    // Sử dụng value từ controller nếu có, nếu không thì dùng props
    const finalValue = controllerField?.value || props.value || '';

    // Wrap onChange
    const finalOnChange = async (value: string) => {
      if (controllerField?.onChange) {
        controllerField.onChange(value);
      } else if (props.onChange) {
        props.onChange(value);
      }
    };

    // Wrap onBlur
    const finalOnBlur = () => {
      if (controllerField?.onBlur) {
        controllerField.onBlur();
      } else if (props.onBlur) {
        props.onBlur();
      }
    };

    // Đảm bảo value được truyền xuống
    const finalProps = {
      ...props,
      value: finalValue,
      onChange: finalOnChange as (internationalPhone: string) => void,
      onBlur: finalOnBlur,
    };

    return (
      <PhoneInputWithCountry
        {...finalProps}
        ref={ref}
        // Không hiển thị error trong component, để FormItem xử lý
        error={undefined}
        helperText={undefined}
      />
    );
  }
);

PhoneInputWithCountryFormField.displayName = 'PhoneInputWithCountryFormField';

export default PhoneInputWithCountryFormField;
