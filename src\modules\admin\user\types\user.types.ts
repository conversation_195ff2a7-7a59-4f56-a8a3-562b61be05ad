/**
 * <PERSON><PERSON><PERSON> kiểu dữ liệu liên quan đến quản lý người dùng
 */
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Trạng thái người dùng
 */
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  DELETED = 'deleted',
}

/**
 * Loại tài khoản người dùng
 */
export enum UserType {
  INDIVIDUAL = 'INDIVIDUAL',
  BUSINESS = 'BUSINESS',
}

/**
 * Giới tính người dùng
 */
export enum UserGender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHER = 'OTHER',
}

/**
 * Thông tin người dùng
 */
export interface User {
  id: number;
  fullName: string;
  email: string;
  phoneNumber: string;
  status: UserStatus;
  type: UserType;
  isActive: boolean;
  isVerifyEmail: boolean;
  isVerifyPhone: boolean;
  pointsBalance: number;
  avatar?: string;
  avatarUrl?: string;
  gender?: UserGender;
  dateOfBirth?: string;
  address?: string;
  createdAt: number;
  updatedAt: number;
}

/**
 * Tham số truy vấn danh sách người dùng
 */
export interface UserQueryDto extends QueryDto {
  status?: UserStatus;
  type?: UserType;
  isVerifyEmail?: boolean;
  isVerifyPhone?: boolean;
  startDate?: string;
  endDate?: string;
}

/**
 * Dữ liệu tạo người dùng mới
 */
export interface CreateUserDto {
  fullName: string;
  email: string;
  phoneNumber: string;
  password: string;
  type: UserType;
  gender?: UserGender;
  dateOfBirth?: string;
  address?: string;
}

/**
 * Dữ liệu cập nhật người dùng
 */
export interface UpdateUserDto {
  fullName?: string;
  email?: string;
  phoneNumber?: string;
  status?: UserStatus;
  type?: UserType;
  gender?: UserGender;
  dateOfBirth?: string;
  address?: string;
}

/**
 * Dữ liệu cập nhật mật khẩu người dùng
 */
export interface UpdateUserPasswordDto {
  password: string;
}

/**
 * Thông tin chi tiết cho block/unblock user
 */
export interface UserActionInfo {
  details: string;
  reportedBy?: string;
  approvedBy?: string;
}

/**
 * Dữ liệu block người dùng
 */
export interface BlockUserDto {
  reason: string;
  info: {
    details: string;
    reportedBy: string;
  };
}

/**
 * Dữ liệu unblock người dùng
 */
export interface UnblockUserDto {
  reason: string;
  info: {
    details: string;
    approvedBy: string;
  };
}

/**
 * Request payload cho block user
 */
export interface BlockUserRequest {
  id: number;
  data: BlockUserDto;
}

/**
 * Request payload cho unblock user
 */
export interface UnblockUserRequest {
  id: number;
  data: UnblockUserDto;
}
