/**
 * Routes cho module Subscription
 */
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import { Loading } from '@/shared/components/common';
import i18n from '@/lib/i18n';
import AdminLayout from '@/shared/layouts/AdminLayout';

// Import Subscription pages
const SubscriptionPlansListPage = lazy(
  () => import('@/modules/admin/subscription/pages/SubscriptionPlansListPage')
);
const PlanPricingListPage = lazy(
  () => import('@/modules/admin/subscription/pages/PlanPricingListPage')
);
const SubscriptionManagementPage = lazy(
  () => import('@/modules/admin/subscription/pages/SubscriptionManagementPage')
);

/**
 * Subscription module routes
 */
export const subscriptionAdminRoutes: RouteObject[] = [
  {
    path: '/admin/subscription',
    element: (
      <AdminLayout title={i18n.t('subscription:title')}>
        <Suspense fallback={<Loading />}>
          <SubscriptionManagementPage />
        </Suspense>
      </AdminLayout>
    )
  },
  {
    path: '/admin/subscription/plans',
    element: (
      <AdminLayout title={i18n.t('subscription:title')}>
        <Suspense fallback={<Loading />}>
          <SubscriptionPlansListPage />
        </Suspense>
      </AdminLayout>
    )
  },
  {
    path: '/admin/subscription/plan-pricing',
    element: (
      <AdminLayout title={i18n.t('subscription:title')}>
        <Suspense fallback={<Loading />}>
          <PlanPricingListPage />
        </Suspense>
      </AdminLayout>
    )
  }
];

export default subscriptionAdminRoutes;
